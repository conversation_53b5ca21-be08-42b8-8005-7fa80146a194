---
import LanguageSwitcher from "../common/LanguageSwitcher.astro";
import ThemeToggle from "../common/ThemeToggle.astro";

interface Props {
  githubUsername?: string;
  i18n: any;
  lang: "fr" | "en";
  initialOpen?: boolean;
}

const {
  githubUsername = "Jamed<PERSON>",
  lang = [
    { code: "fr", label: "Français" },
    { code: "en", label: "English" },
  ],
  initialOpen = false,
} = Astro.props as Props;

const { i18n } = Astro.props;
---

<!--
QuickAccessMenu.astro
• Menu plein écran qui bloque le scroll
• Thème: clair/sombre/système (persisté)
• Langue: liste dynamique via props
• Activité GitHub publique (10 derniers events)
• Raccourcis en bas à gauche (Ctrl/⌘+K, T, L, Échap)


Utilisation:
<QuickAccessMenu githubUsername="tonUser" languages={[{code:'fr',label:'Français'},{code:'en',label:'English'}]} />


Styles: autonome via <style> ci-dessous (pas besoin de Tailwind/React)
-->
<script>
  // Cible le conteneur et l’élément <html>
  const root = document.getElementById("quick-access-menu");
  const html = document.documentElement;

  // Applique / retire le scroll lock selon data-open
  function applyScrollLock() {
    const isOpen = root.getAttribute("data-open") === "1";
    html.style.overflow = isOpen ? "hidden" : ""; // verrouille / libère
  }

  // 1) appliquer au chargement
  applyScrollLock();

  // 2) rester sync quand data-open change (depuis un bouton, une hotkey, etc.)
  const obs = new MutationObserver(applyScrollLock);
  obs.observe(root, { attributes: true, attributeFilter: ["data-open"] });

  // 3) (optionnel pour tester) hotkeys:
  //    Ctrl/⌘+K -> toggle ; Échap -> fermer
  window.addEventListener("keydown", (e) => {
    const isMod = navigator.platform.includes("Mac") ? e.metaKey : e.ctrlKey;
    if (isMod && e.key.toLowerCase() === "k") {
      e.preventDefault();
      const isOpen = root.getAttribute("data-open") === "1";
      root.setAttribute("data-open", isOpen ? "0" : "1");
    } else if (e.key === "Escape") {
      root.setAttribute("data-open", "0");
    }
  });
</script>

<section
  id="quick-access-menu"
  class="qa-root"
  data-open={initialOpen ? "1" : "0"}
  data-lang={lang || "fr"}
>
  <div class="qa-overlay"></div>
  <div
    class="qa-panel"
    role="dialog"
    aria-modal="true"
    aria-label="Quick Access"
    data-qa-panel
  >
  </div>
</section>

<style>
  #quick-access-menu {
    position: fixed;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;

    width: 100%;
    height: 100vh;

    pointer-events: none;
    transition: opacity 0.3s ease;
  }
  #quick-access-menu[data-open="1"] {
    opacity: 1;
    pointer-events: auto;
  }
  #quick-access-menu[data-open="0"] {
    opacity: 0;
    pointer-events: none;
  }

  .qa-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(2px);
    z-index: 49;
  }
</style>
